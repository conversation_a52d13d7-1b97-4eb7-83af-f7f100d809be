{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cantieri/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cantieriApi } from '@/lib/api'\nimport { Cantiere } from '@/types'\nimport {\n  Building2,\n  Plus,\n  Settings,\n  Trash2,\n  Eye,\n  Search,\n  Copy,\n  Calendar,\n  MapPin,\n  User,\n  Loader2,\n  AlertCircle,\n  Lock,\n  Mail,\n  Shield,\n  CheckCircle\n} from 'lucide-react'\n\nexport default function CantieriPage() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n  const [cantieri, setCantieri] = useState<Cantiere[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [showPasswordDialog, setShowPasswordDialog] = useState(false)\n  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)\n  const [formData, setFormData] = useState({\n    commessa: '',\n    descrizione: '',\n    nome_cliente: '',\n    indirizzo_cantiere: '',\n    citta_cantiere: '',\n    nazione_cantiere: '',\n    password_cantiere: '',\n    codice_univoco: ''\n  })\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  })\n  const [passwordMode, setPasswordMode] = useState<'change' | 'recover' | 'view'>('change')\n  const [revealedPassword, setRevealedPassword] = useState('')\n  const [showRevealedPassword, setShowRevealedPassword] = useState(false)\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadCantieri()\n    }\n  }, [isAuthenticated])\n\n  const loadCantieri = async () => {\n    try {\n      setLoading(true)\n      const data = await cantieriApi.getCantieri()\n      setCantieri(data)\n    } catch (error) {\n      console.error('Errore nel caricamento cantieri:', error)\n      setError('Errore nel caricamento dei cantieri')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCreateCantiere = async () => {\n    try {\n      await cantieriApi.createCantiere(formData)\n      setShowCreateDialog(false)\n      setFormData({\n        commessa: '',\n        descrizione: '',\n        nome_cliente: '',\n        indirizzo_cantiere: '',\n        citta_cantiere: '',\n        nazione_cantiere: '',\n        password_cantiere: '',\n        codice_univoco: ''\n      })\n      loadCantieri()\n    } catch (error) {\n      console.error('Errore nella creazione cantiere:', error)\n      setError('Errore nella creazione del cantiere')\n    }\n  }\n\n  const handleEditCantiere = async () => {\n    if (!selectedCantiere) return\n    \n    try {\n      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)\n      setShowEditDialog(false)\n      setSelectedCantiere(null)\n      loadCantieri()\n    } catch (error) {\n      console.error('Errore nella modifica cantiere:', error)\n      setError('Errore nella modifica del cantiere')\n    }\n  }\n\n  const handleSelectCantiere = (cantiere: Cantiere) => {\n    // Salva il cantiere selezionato nel localStorage\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())\n    localStorage.setItem('selectedCantiereName', cantiere.commessa)\n    \n    // Naviga alla pagina del cantiere specifico\n    router.push(`/cantieri/${cantiere.id_cantiere}`)\n  }\n\n  const openEditDialog = (cantiere: Cantiere) => {\n    setSelectedCantiere(cantiere)\n    setFormData({\n      commessa: cantiere.commessa || '',\n      descrizione: cantiere.descrizione || '',\n      nome_cliente: cantiere.nome_cliente || '',\n      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',\n      citta_cantiere: cantiere.citta_cantiere || '',\n      nazione_cantiere: cantiere.nazione_cantiere || '',\n      password_cantiere: cantiere.password_cantiere || '',\n      codice_univoco: cantiere.codice_univoco || ''\n    })\n    setShowEditDialog(true)\n  }\n\n  const handleRecoverPasswordDirect = async () => {\n    if (!selectedCantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      console.log('Recupero diretto password per cantiere:', selectedCantiere.id_cantiere)\n\n      // Chiamata API reale per recupero diretto password\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/view-password`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('access_token')}`\n        }\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore nel recupero password')\n      }\n\n      const data = await response.json()\n      setRevealedPassword(data.password_cantiere)\n      setShowRevealedPassword(true)\n      setError('')\n\n    } catch (error) {\n      console.error('Errore nel recupero password:', error)\n      setError(error instanceof Error ? error.message : 'Errore nel recupero password')\n      setShowRevealedPassword(false)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSendPasswordByEmail = async () => {\n    if (!selectedCantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      console.log('Invio password via email per cantiere:', selectedCantiere.id_cantiere)\n\n      // Chiamata API reale per invio password via email\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/send-password-email`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('access_token')}`\n        }\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore nell\\'invio email')\n      }\n\n      const data = await response.json()\n      alert(data.message || 'Password inviata via email con successo')\n      setError('')\n\n    } catch (error) {\n      console.error('Errore nell\\'invio email:', error)\n      setError(error instanceof Error ? error.message : 'Errore nell\\'invio email')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChangePassword = async () => {\n    if (!selectedCantiere) return\n\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setError('Le password non coincidono')\n      return\n    }\n\n    if (!passwordData.currentPassword) {\n      setError('Inserisci la password attuale per confermare il cambio')\n      return\n    }\n\n    if (!passwordData.newPassword || passwordData.newPassword.length < 6) {\n      setError('La nuova password deve essere di almeno 6 caratteri')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      console.log('Cambio password per cantiere:', selectedCantiere.id_cantiere)\n\n      // Chiamata API reale per cambio password\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/change-password`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('access_token')}`\n        },\n        body: JSON.stringify({\n          password_attuale: passwordData.currentPassword,\n          password_nuova: passwordData.newPassword,\n          conferma_password: passwordData.confirmPassword\n        })\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore nel cambio password')\n      }\n\n      const data = await response.json()\n\n      if (data.success) {\n        setPasswordData({\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        })\n        setShowPasswordDialog(false)\n        setError('')\n        alert(data.message || 'Password cambiata con successo')\n      } else {\n        throw new Error(data.message || 'Errore nel cambio password')\n      }\n\n    } catch (error) {\n      console.error('Errore nel cambio password:', error)\n      setError(error instanceof Error ? error.message : 'Errore nel cambio password')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      // Visual feedback could be added here (toast notification)\n      console.log('Codice copiato:', text)\n    } catch (err) {\n      console.error('Errore nella copia:', err)\n    }\n  }\n\n  const filteredCantieri = cantieri.filter(cantiere =>\n    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-[90%] mx-auto p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center gap-4\">\n          <div className=\"relative w-80\">\n            <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Cerca cantieri...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-8 w-full\"\n            />\n          </div>\n        </div>\n        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n          <DialogTrigger asChild>\n            <Button className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Nuovo Cantiere\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-[425px]\">\n            <DialogHeader>\n              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n              <DialogDescription>\n                Inserisci i dettagli del nuovo cantiere\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"grid gap-4 py-4\">\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"commessa\" className=\"text-right\">\n                  Commessa\n                </Label>\n                <Input\n                  id=\"commessa\"\n                  value={formData.commessa}\n                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"descrizione\" className=\"text-right\">\n                  Descrizione\n                </Label>\n                <Input\n                  id=\"descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"nome_cliente\" className=\"text-right\">\n                  Cliente\n                </Label>\n                <Input\n                  id=\"nome_cliente\"\n                  value={formData.nome_cliente}\n                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"password_cantiere\" className=\"text-right\">\n                  Password\n                </Label>\n                <Input\n                  id=\"password_cantiere\"\n                  type=\"password\"\n                  value={formData.password_cantiere}\n                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n            </div>\n            <DialogFooter>\n              <Button onClick={handleCreateCantiere} className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">Crea Cantiere</Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 border border-red-200 rounded-lg bg-red-50\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-4 w-4 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n\n\n      {loading ? (\n        <div className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"h-8 w-8 animate-spin\" />\n        </div>\n      ) : filteredCantieri.length === 0 ? (\n        <Card>\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Building2 className=\"h-12 w-12 text-muted-foreground mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Nessun cantiere trovato</h3>\n            <p className=\"text-muted-foreground text-center mb-4\">\n              {searchTerm ? 'Nessun cantiere corrisponde ai criteri di ricerca' : 'Crea il tuo primo cantiere per iniziare'}\n            </p>\n            {!searchTerm && (\n              <Button\n                onClick={() => setShowCreateDialog(true)}\n                className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n              >\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Crea Primo Cantiere\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Commessa</TableHead>\n                <TableHead>Descrizione</TableHead>\n                <TableHead>Cliente</TableHead>\n                <TableHead>Data Creazione</TableHead>\n                <TableHead>Codice</TableHead>\n                <TableHead>Password</TableHead>\n                <TableHead className=\"text-right\">Azioni</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredCantieri.map((cantiere) => (\n                <TableRow key={cantiere.id_cantiere}>\n                  <TableCell className=\"font-medium\">{cantiere.commessa}</TableCell>\n                  <TableCell>{cantiere.descrizione}</TableCell>\n                  <TableCell>{cantiere.nome_cliente}</TableCell>\n                  <TableCell>{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <code className=\"text-sm bg-muted px-2 py-1 rounded\">\n                      {cantiere.codice_univoco}\n                    </code>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center gap-2\">\n                      <code className=\"text-sm bg-green-100 text-green-800 px-2 py-1 rounded\">\n                        {cantiere.password_cantiere ? '••••••••' : 'Non impostata'}\n                      </code>\n                      <Button\n                        size=\"sm\"\n                        variant=\"ghost\"\n                        className=\"text-blue-600 hover:bg-blue-50 p-1\"\n                        title=\"Gestisci password cantiere\"\n                        onClick={() => {\n                          setSelectedCantiere(cantiere)\n                          setShowPasswordDialog(true)\n                        }}\n                      >\n                        <Lock className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </TableCell>\n                  <TableCell className=\"text-right\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        onClick={() => openEditDialog(cantiere)}\n                        className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n                        title=\"Modifica cantiere\"\n                      >\n                        <Settings className=\"h-3 w-3\" />\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleSelectCantiere(cantiere)}\n                        className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n                      >\n                        Gestisci\n                        <Eye className=\"ml-2 h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </Card>\n      )}\n\n      {/* Dialog di modifica */}\n      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>\n        <DialogContent className=\"sm:max-w-[425px]\">\n          <DialogHeader>\n            <DialogTitle>Modifica Cantiere</DialogTitle>\n            <DialogDescription>\n              Modifica i dettagli del cantiere selezionato\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-commessa\" className=\"text-right\">\n                Commessa\n              </Label>\n              <Input\n                id=\"edit-commessa\"\n                value={formData.commessa}\n                onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-descrizione\" className=\"text-right\">\n                Descrizione\n              </Label>\n              <Input\n                id=\"edit-descrizione\"\n                value={formData.descrizione}\n                onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-nome_cliente\" className=\"text-right\">\n                Cliente\n              </Label>\n              <Input\n                id=\"edit-nome_cliente\"\n                value={formData.nome_cliente}\n                onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-indirizzo_cantiere\" className=\"text-right\">\n                Indirizzo\n              </Label>\n              <Input\n                id=\"edit-indirizzo_cantiere\"\n                value={formData.indirizzo_cantiere}\n                onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-citta_cantiere\" className=\"text-right\">\n                Città\n              </Label>\n              <Input\n                id=\"edit-citta_cantiere\"\n                value={formData.citta_cantiere}\n                onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-nazione_cantiere\" className=\"text-right\">\n                Nazione\n              </Label>\n              <Input\n                id=\"edit-nazione_cantiere\"\n                value={formData.nazione_cantiere}\n                onChange={(e) => setFormData({ ...formData, nazione_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-password_cantiere\" className=\"text-right\">\n                Password\n              </Label>\n              <Input\n                id=\"edit-password_cantiere\"\n                type=\"password\"\n                value={formData.password_cantiere}\n                onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setShowEditDialog(false)} className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">\n              Annulla\n            </Button>\n            <Button onClick={handleEditCantiere} className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">Salva Modifiche</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog gestione password ottimale */}\n      <Dialog open={showPasswordDialog} onOpenChange={(open) => {\n        setShowPasswordDialog(open)\n        if (!open) {\n          setPasswordMode('change')\n          setShowRevealedPassword(false)\n          setRevealedPassword('')\n          setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })\n        }\n      }}>\n        <DialogContent className=\"sm:max-w-[600px]\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Lock className=\"h-5 w-5\" />\n              Gestione Password - {selectedCantiere?.commessa}\n            </DialogTitle>\n            <DialogDescription>\n              Scegli come gestire la password del cantiere\n            </DialogDescription>\n          </DialogHeader>\n\n          {/* Tabs per le diverse modalità */}\n          <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg\">\n            <button\n              onClick={() => setPasswordMode('change')}\n              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                passwordMode === 'change'\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <Settings className=\"inline mr-2 h-4 w-4\" />\n              Cambia\n            </button>\n            <button\n              onClick={() => setPasswordMode('recover')}\n              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                passwordMode === 'recover'\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <Mail className=\"inline mr-2 h-4 w-4\" />\n              Recupera\n            </button>\n          </div>\n\n          <div className=\"space-y-6\">\n\n            {/* Modalità Cambia Password */}\n            {passwordMode === 'change' && (\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-medium flex items-center gap-2\">\n                  <Settings className=\"h-5 w-5\" />\n                  Cambia Password\n                </h3>\n                <p className=\"text-sm text-gray-600\">\n                  Inserisci la password attuale e la nuova password\n                </p>\n                <div className=\"space-y-3\">\n                  <div>\n                    <Label htmlFor=\"current-password-change\">Password Attuale</Label>\n                    <Input\n                      id=\"current-password-change\"\n                      type=\"password\"\n                      placeholder=\"Password attuale per conferma\"\n                      value={passwordData.currentPassword}\n                      onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"new-password\">Nuova Password</Label>\n                    <Input\n                      id=\"new-password\"\n                      type=\"password\"\n                      placeholder=\"Inserisci la nuova password\"\n                      value={passwordData.newPassword}\n                      onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"confirm-password\">Conferma Nuova Password</Label>\n                    <Input\n                      id=\"confirm-password\"\n                      type=\"password\"\n                      placeholder=\"Conferma la nuova password\"\n                      value={passwordData.confirmPassword}\n                      onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}\n                    />\n                  </div>\n                  <Button\n                    onClick={handleChangePassword}\n                    disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}\n                    className=\"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n                  >\n                    {loading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Settings className=\"mr-2 h-4 w-4\" />}\n                    Cambia Password\n                  </Button>\n                </div>\n              </div>\n            )}\n\n            {/* Modalità Recupera Password */}\n            {passwordMode === 'recover' && (\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-medium flex items-center gap-2\">\n                  <Mail className=\"h-5 w-5\" />\n                  Recupera Password\n                </h3>\n                <p className=\"text-sm text-gray-600\">\n                  Opzioni per recuperare una password dimenticata\n                </p>\n\n                <div className=\"space-y-4\">\n                  {/* Recupero diretto */}\n                  <div className=\"p-4 border border-blue-200 rounded-lg\">\n                    <h4 className=\"font-medium text-blue-800 mb-2\">Recupero Diretto</h4>\n                    <p className=\"text-sm text-blue-700 mb-3\">\n                      Tenta di recuperare la password dal sistema (funziona solo se la password è stata salvata in formato recuperabile)\n                    </p>\n                    {showRevealedPassword && (\n                      <div className=\"mb-3 p-3 bg-green-50 border border-green-200 rounded\">\n                        <div className=\"flex items-center gap-2 mb-2\">\n                          <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                          <span className=\"font-medium text-green-800\">Password Recuperata</span>\n                        </div>\n                        <code className=\"text-lg font-mono bg-white p-2 rounded border block\">{revealedPassword}</code>\n                      </div>\n                    )}\n                    <Button\n                      onClick={handleRecoverPasswordDirect}\n                      disabled={loading}\n                      className=\"w-full relative overflow-hidden bg-orange-600 hover:bg-orange-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-orange-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n                    >\n                      {loading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Shield className=\"mr-2 h-4 w-4\" />}\n                      Recupera Password\n                    </Button>\n                  </div>\n\n                  {/* Invio via email */}\n                  <div className=\"p-4 border border-green-200 rounded-lg\">\n                    <h4 className=\"font-medium text-green-800 mb-2\">Invio via Email</h4>\n                    <p className=\"text-sm text-green-700 mb-3\">\n                      Invia la password all'indirizzo email dell'amministratore del cantiere\n                    </p>\n                    <Button\n                      onClick={handleSendPasswordByEmail}\n                      disabled={loading}\n                      className=\"w-full relative overflow-hidden bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-green-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n                    >\n                      {loading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Mail className=\"mr-2 h-4 w-4\" />}\n                      Invia Password via Email\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Messaggio di errore */}\n            {error && (\n              <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                <div className=\"flex items-center gap-2\">\n                  <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                  <span className=\"font-medium text-red-800\">Errore</span>\n                </div>\n                <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n              </div>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowPasswordDialog(false)}\n            >\n              Chiudi\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;IAClB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,iHAAA,CAAA,cAAW,CAAC,WAAW;YAC1C,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,iHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YACjC,oBAAoB;YACpB,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;gBACd,oBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;gBAClB,mBAAmB;gBACnB,gBAAgB;YAClB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,iHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,iBAAiB,WAAW,EAAE;YAC/D,kBAAkB;YAClB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iDAAiD;QACjD,aAAa,OAAO,CAAC,sBAAsB,SAAS,WAAW,CAAC,QAAQ;QACxE,aAAa,OAAO,CAAC,wBAAwB,SAAS,QAAQ;QAE9D,4CAA4C;QAC5C,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,WAAW,EAAE;IACjD;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;QACpB,YAAY;YACV,UAAU,SAAS,QAAQ,IAAI;YAC/B,aAAa,SAAS,WAAW,IAAI;YACrC,cAAc,SAAS,YAAY,IAAI;YACvC,oBAAoB,SAAS,kBAAkB,IAAI;YACnD,gBAAgB,SAAS,cAAc,IAAI;YAC3C,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,mBAAmB,SAAS,iBAAiB,IAAI;YACjD,gBAAgB,SAAS,cAAc,IAAI;QAC7C;QACA,kBAAkB;IACpB;IAEA,MAAM,8BAA8B;QAClC,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,WAAW;YACX,SAAS;YAET,QAAQ,GAAG,CAAC,2CAA2C,iBAAiB,WAAW;YAEnF,mDAAmD;YACnD,MAAM,aAAa,6DAAmC;YACtD,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,iBAAiB,WAAW,CAAC,cAAc,CAAC,EAAE;gBACvG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACnE;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,oBAAoB,KAAK,iBAAiB;YAC1C,wBAAwB;YACxB,SAAS;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,wBAAwB;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,WAAW;YACX,SAAS;YAET,QAAQ,GAAG,CAAC,0CAA0C,iBAAiB,WAAW;YAElF,kDAAkD;YAClD,MAAM,aAAa,6DAAmC;YACtD,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,iBAAiB,WAAW,CAAC,oBAAoB,CAAC,EAAE;gBAC7G,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACnE;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,KAAK,OAAO,IAAI;YACtB,SAAS;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,kBAAkB;QAEvB,IAAI,aAAa,WAAW,KAAK,aAAa,eAAe,EAAE;YAC7D,SAAS;YACT;QACF;QAEA,IAAI,CAAC,aAAa,eAAe,EAAE;YACjC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,MAAM,GAAG,GAAG;YACpE,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,QAAQ,GAAG,CAAC,iCAAiC,iBAAiB,WAAW;YAEzE,yCAAyC;YACzC,MAAM,aAAa,6DAAmC;YACtD,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,iBAAiB,WAAW,CAAC,gBAAgB,CAAC,EAAE;gBACzG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACnE;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,kBAAkB,aAAa,eAAe;oBAC9C,gBAAgB,aAAa,WAAW;oBACxC,mBAAmB,aAAa,eAAe;gBACjD;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB;oBACd,iBAAiB;oBACjB,aAAa;oBACb,iBAAiB;gBACnB;gBACA,sBAAsB;gBACtB,SAAS;gBACT,MAAM,KAAK,OAAO,IAAI;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,mBAAmB;QACjC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,WACvC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,SAAS,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OACnE,SAAS,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW;IAGtE,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;kCAIhB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAkB,cAAc;;0CAC5C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAa;;;;;;kEAGjD,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAc,WAAU;kEAAa;;;;;;kEAGpD,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAe,WAAU;kEAAa;;;;;;kEAGrD,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACzE,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAoB,WAAU;kEAAa;;;;;;kEAG1D,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,iBAAiB;wDACjC,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC9E,WAAU;;;;;;;;;;;;;;;;;;kDAIhB,8OAAC,kIAAA,CAAA,eAAY;kDACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAsB,WAAU;sDAA6a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMre,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAOrC,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;uBAEnB,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCACV,aAAa,sDAAsD;;;;;;wBAErE,CAAC,4BACA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,oBAAoB;4BACnC,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAOzC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAa;;;;;;;;;;;;;;;;;sCAGtC,8OAAC,iIAAA,CAAA,YAAS;sCACP,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAe,SAAS,QAAQ;;;;;;sDACrD,8OAAC,iIAAA,CAAA,YAAS;sDAAE,SAAS,WAAW;;;;;;sDAChC,8OAAC,iIAAA,CAAA,YAAS;sDAAE,SAAS,YAAY;;;;;;sDACjC,8OAAC,iIAAA,CAAA,YAAS;sDAAE,IAAI,KAAK,SAAS,cAAc,EAAE,kBAAkB;;;;;;sDAChE,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,cAAc;;;;;;;;;;;sDAG5B,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,SAAS,iBAAiB,GAAG,aAAa;;;;;;kEAE7C,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACV,OAAM;wDACN,SAAS;4DACP,oBAAoB;4DACpB,sBAAsB;wDACxB;kEAEA,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAItB,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,qBAAqB;wDACpC,WAAU;;4DACX;0EAEC,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mCA7CR,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;0BAyD7C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAa;;;;;;sDAGtD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrE,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAmB,WAAU;sDAAa;;;;;;sDAGzD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAoB,WAAU;sDAAa;;;;;;sDAG1D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,YAAY;4CAC5B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAA0B,WAAU;sDAAa;;;;;;sDAGhE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,kBAAkB;4CAClC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/E,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAsB,WAAU;sDAAa;;;;;;sDAG5D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC3E,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAwB,WAAU;sDAAa;;;;;;sDAG9D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,gBAAgB;4CAChC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7E,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAyB,WAAU;sDAAa;;;;;;sDAG/D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC9E,WAAU;;;;;;;;;;;;;;;;;;sCAIhB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,kBAAkB;oCAAQ,WAAU;8CAA6a;;;;;;8CAGxe,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,WAAU;8CAA6a;;;;;;;;;;;;;;;;;;;;;;;0BAMle,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAoB,cAAc,CAAC;oBAC/C,sBAAsB;oBACtB,IAAI,CAAC,MAAM;wBACT,gBAAgB;wBAChB,wBAAwB;wBACxB,oBAAoB;wBACpB,gBAAgB;4BAAE,iBAAiB;4BAAI,aAAa;4BAAI,iBAAiB;wBAAG;oBAC9E;gBACF;0BACE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;wCACP,kBAAkB;;;;;;;8CAEzC,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,kEAAkE,EAC5E,iBAAiB,WACb,qCACA,qCACJ;;sDAEF,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;8CAG9C,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,kEAAkE,EAC5E,iBAAiB,YACb,qCACA,qCACJ;;sDAEF,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;gCAGZ,iBAAiB,0BAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGlC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDAGrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAA0B;;;;;;sEACzC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,OAAO,aAAa,eAAe;4DACnC,UAAU,CAAC,IAAM,gBAAgB;oEAAE,GAAG,YAAY;oEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAAC;;;;;;;;;;;;8DAGxF,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,OAAO,aAAa,WAAW;4DAC/B,UAAU,CAAC,IAAM,gBAAgB;oEAAE,GAAG,YAAY;oEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gEAAC;;;;;;;;;;;;8DAGpF,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,OAAO,aAAa,eAAe;4DACnC,UAAU,CAAC,IAAM,gBAAgB;oEAAE,GAAG,YAAY;oEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAAC;;;;;;;;;;;;8DAGxF,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,WAAW,CAAC,aAAa,eAAe,IAAI,CAAC,aAAa,WAAW,IAAI,CAAC,aAAa,eAAe;oDAChH,WAAU;;wDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAAiC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAkB;;;;;;;;;;;;;;;;;;;gCAQ5G,iBAAiB,2BAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG9B,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDAIrC,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;wDAGzC,sCACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;8EAE/C,8OAAC;oEAAK,WAAU;8EAAuD;;;;;;;;;;;;sEAG3E,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;;gEAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAAiC,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAkB;;;;;;;;;;;;;8DAMvG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAkC;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;sEAG3C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;;gEAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAAiC,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAkB;;;;;;;;;;;;;;;;;;;;;;;;;gCAS1G,uBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE7C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAKhD,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,sBAAsB;0CACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}