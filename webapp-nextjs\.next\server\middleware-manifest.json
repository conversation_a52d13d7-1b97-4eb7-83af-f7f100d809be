{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "57ef6d1fe55c25c86a5095e04033d1c4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5961bab404f8966f2f59c8b47adfa112613f007a128421e25d00d7bb51aea459", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cbcf37f96ce98e74bb203c51c244270ddbeaa66ca79b78cb56b2456af9728ba6"}}}, "sortedMiddleware": ["/"], "functions": {}}