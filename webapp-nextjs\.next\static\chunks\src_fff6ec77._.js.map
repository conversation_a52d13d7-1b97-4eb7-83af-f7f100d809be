{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/parco-cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Progress } from '@/components/ui/progress'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { parcoCaviApi } from '@/lib/api'\nimport { ParcoCavo } from '@/types'\nimport { \n  Package, \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Eye,\n  Download,\n  Upload,\n  Loader2\n} from 'lucide-react'\n\nexport default function ParcoCaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [bobine, setBobine] = useState<ParcoCavo[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, cantiere } = useAuth()\n\n  // Carica le bobine dal backend\n  useEffect(() => {\n    loadBobine()\n  }, [])\n\n  const loadBobine = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n      \n      const cantiereId = cantiere?.id_cantiere || user?.id_utente\n      if (!cantiereId) {\n        setError('Cantiere non selezionato')\n        return\n      }\n\n      const data = await parcoCaviApi.getBobine(cantiereId)\n      setBobine(data)\n    } catch (error: any) {\n      console.error('Errore caricamento bobine:', error)\n      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Ricarica quando cambiano i filtri\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      loadBobine()\n    }, 500)\n\n    return () => clearTimeout(timeoutId)\n  }, [searchTerm, selectedStatus])\n\n  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {\n    const percentualeResidue = metri_totali > 0 ? (metri_residui / metri_totali) * 100 : 0\n    \n    if (percentualeResidue === 0) {\n      return <Badge className=\"bg-red-100 text-red-800\">Esaurita</Badge>\n    } else if (percentualeResidue < 20) {\n      return <Badge className=\"bg-orange-100 text-orange-800\">Quasi Esaurita</Badge>\n    } else if (percentualeResidue < 50) {\n      return <Badge className=\"bg-yellow-100 text-yellow-800\">In Uso</Badge>\n    } else {\n      return <Badge className=\"bg-green-100 text-green-800\">Disponibile</Badge>\n    }\n  }\n\n  const filteredBobine = bobine.filter(bobina => {\n    const matchesSearch = bobina.id_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    let matchesStatus = true\n    if (selectedStatus !== 'all') {\n      const percentualeResidue = bobina.metri_totali > 0 ? (bobina.metri_residui / bobina.metri_totali) * 100 : 0\n      switch (selectedStatus) {\n        case 'disponibile':\n          matchesStatus = percentualeResidue >= 50\n          break\n        case 'in_uso':\n          matchesStatus = percentualeResidue > 0 && percentualeResidue < 50\n          break\n        case 'esaurita':\n          matchesStatus = percentualeResidue === 0\n          break\n      }\n    }\n    \n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: bobine.length,\n    disponibili: bobine.filter(b => b.metri_totali > 0 ? (b.metri_residui / b.metri_totali) >= 0.5 : false).length,\n    in_uso: bobine.filter(b => {\n      const perc = b.metri_totali > 0 ? (b.metri_residui / b.metri_totali) : 0\n      return perc > 0 && perc < 0.5\n    }).length,\n    esaurite: bobine.filter(b => b.metri_residui === 0).length\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        \n        {/* Action buttons */}\n        <div className=\"flex justify-end gap-2 mb-6\">\n          <Button variant=\"outline\" size=\"sm\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Esporta\n          </Button>\n          <Button variant=\"outline\" size=\"sm\">\n            <Upload className=\"h-4 w-4 mr-2\" />\n            Importa\n          </Button>\n          <Button size=\"sm\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Nuova Bobina\n          </Button>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Totali</p>\n                  <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n                </div>\n                <Package className=\"h-8 w-8 text-blue-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Disponibili</p>\n                  <p className=\"text-2xl font-bold text-green-600\">{stats.disponibili}</p>\n                </div>\n                <CheckCircle className=\"h-8 w-8 text-green-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">In Uso</p>\n                  <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_uso}</p>\n                </div>\n                <Clock className=\"h-8 w-8 text-yellow-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Esaurite</p>\n                  <p className=\"text-2xl font-bold text-red-600\">{stats.esaurite}</p>\n                </div>\n                <AlertCircle className=\"h-8 w-8 text-red-500\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per ID bobina, numero, tipologia o utility...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'disponibile', 'in_uso', 'esaurita'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutte' : \n                     status === 'disponibile' ? 'Disponibili' :\n                     status === 'in_uso' ? 'In Uso' : 'Esaurite'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Bobine Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>\n            <CardDescription>\n              Gestione completa delle bobine con stato utilizzo e metrature\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>ID Bobina</TableHead>\n                    <TableHead>Numero</TableHead>\n                    <TableHead>Utility</TableHead>\n                    <TableHead>Tipologia</TableHead>\n                    <TableHead>Conduttori/Sezione</TableHead>\n                    <TableHead>Metrature</TableHead>\n                    <TableHead>Utilizzo</TableHead>\n                    <TableHead>Stato</TableHead>\n                    <TableHead>Ubicazione</TableHead>\n                    <TableHead>Azioni</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {isLoading ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          Caricamento bobine...\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : error ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                          <AlertCircle className=\"h-4 w-4\" />\n                          {error}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : filteredBobine.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8 text-slate-500\">\n                        Nessuna bobina trovata\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredBobine.map((bobina) => {\n                      const percentualeUtilizzo = bobina.metri_totali > 0 ? \n                        ((bobina.metri_totali - bobina.metri_residui) / bobina.metri_totali) * 100 : 0\n                      \n                      return (\n                        <TableRow key={bobina.id_bobina}>\n                          <TableCell className=\"font-medium\">{bobina.id_bobina}</TableCell>\n                          <TableCell>{bobina.numero_bobina || '-'}</TableCell>\n                          <TableCell>{bobina.utility || '-'}</TableCell>\n                          <TableCell>{bobina.tipologia || '-'}</TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              <div>{bobina.n_conduttori || '-'}</div>\n                              <div className=\"text-slate-500\">{bobina.sezione || '-'}</div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              <div>Residui: <span className=\"font-medium\">{bobina.metri_residui}m</span></div>\n                              <div className=\"text-slate-500\">Totali: {bobina.metri_totali}m</div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"space-y-1\">\n                              <div className=\"text-sm font-medium\">{Math.round(percentualeUtilizzo)}%</div>\n                              <Progress value={percentualeUtilizzo} className=\"h-2\" />\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}\n                          </TableCell>\n                          <TableCell>\n                            <Badge variant=\"outline\">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"flex gap-1\">\n                              <Button variant=\"ghost\" size=\"sm\">\n                                <Eye className=\"h-4 w-4\" />\n                              </Button>\n                              <Button variant=\"ghost\" size=\"sm\">\n                                <Edit className=\"h-4 w-4\" />\n                              </Button>\n                              <Button variant=\"ghost\" size=\"sm\">\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      )\n                    })\n                  )}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEjC,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,aAAa,UAAU,eAAe,MAAM;YAClD,IAAI,CAAC,YAAY;gBACf,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAC1C,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,YAAY;qDAAW;oBAC3B;gBACF;oDAAG;YAEH;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;QAAY;KAAe;IAE/B,MAAM,iBAAiB,CAAC,OAAe,eAAuB;QAC5D,MAAM,qBAAqB,eAAe,IAAI,AAAC,gBAAgB,eAAgB,MAAM;QAErF,IAAI,uBAAuB,GAAG;YAC5B,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA0B;;;;;;QACpD,OAAO,IAAI,qBAAqB,IAAI;YAClC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAgC;;;;;;QAC1D,OAAO,IAAI,qBAAqB,IAAI;YAClC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAgC;;;;;;QAC1D,OAAO;YACL,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA8B;;;;;;QACxD;IACF;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,OAAO,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACnE,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,OAAO,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAElF,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,OAAO;YAC5B,MAAM,qBAAqB,OAAO,YAAY,GAAG,IAAI,AAAC,OAAO,aAAa,GAAG,OAAO,YAAY,GAAI,MAAM;YAC1G,OAAQ;gBACN,KAAK;oBACH,gBAAgB,sBAAsB;oBACtC;gBACF,KAAK;oBACH,gBAAgB,qBAAqB,KAAK,qBAAqB;oBAC/D;gBACF,KAAK;oBACH,gBAAgB,uBAAuB;oBACvC;YACJ;QACF;QAEA,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,OAAO,MAAM;QACrB,aAAa,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,GAAG,IAAI,AAAC,EAAE,aAAa,GAAG,EAAE,YAAY,IAAK,MAAM,OAAO,MAAM;QAC9G,QAAQ,OAAO,MAAM,CAAC,CAAA;YACpB,MAAM,OAAO,EAAE,YAAY,GAAG,IAAK,EAAE,aAAa,GAAG,EAAE,YAAY,GAAI;YACvE,OAAO,OAAO,KAAK,OAAO;QAC5B,GAAG,MAAM;QACT,UAAU,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,GAAG,MAAM;IAC5D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGvC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGrC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;;8CACX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAMrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqC,MAAM,MAAM;;;;;;;;;;;;sDAEhE,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKzB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqC,MAAM,WAAW;;;;;;;;;;;;sDAErE,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK7B,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAsC,MAAM,MAAM;;;;;;;;;;;;sDAEjE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAmC,MAAM,QAAQ;;;;;;;;;;;;sDAEhE,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIlC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAO;4CAAe;4CAAU;yCAAW,CAAC,GAAG,CAAC,CAAC,uBACjD,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,mBAAmB,SAAS,YAAY;gDACjD,MAAK;gDACL,SAAS,IAAM,kBAAkB;0DAEhC,WAAW,QAAQ,UACnB,WAAW,gBAAgB,gBAC3B,WAAW,WAAW,WAAW;+CAP7B;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgBjB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;;wCAAC;wCAAgB,eAAe,MAAM;wCAAC;;;;;;;8CACjD,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sDACJ,6LAAC,oIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,6LAAC,oIAAA,CAAA,YAAS;sDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAyB;;;;;;;;;;;;;;;;uDAKhD,sBACF,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB;;;;;;;;;;;;;;;;uDAIL,eAAe,MAAM,KAAK,kBAC5B,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAAkC;;;;;;;;;;uDAKtE,eAAe,GAAG,CAAC,CAAC;gDAClB,MAAM,sBAAsB,OAAO,YAAY,GAAG,IAChD,AAAC,CAAC,OAAO,YAAY,GAAG,OAAO,aAAa,IAAI,OAAO,YAAY,GAAI,MAAM;gDAE/E,qBACE,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAe,OAAO,SAAS;;;;;;sEACpD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,OAAO,aAAa,IAAI;;;;;;sEACpC,6LAAC,oIAAA,CAAA,YAAS;sEAAE,OAAO,OAAO,IAAI;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,YAAS;sEAAE,OAAO,SAAS,IAAI;;;;;;sEAChC,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK,OAAO,YAAY,IAAI;;;;;;kFAC7B,6LAAC;wEAAI,WAAU;kFAAkB,OAAO,OAAO,IAAI;;;;;;;;;;;;;;;;;sEAGvD,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAI;0FAAS,6LAAC;gFAAK,WAAU;;oFAAe,OAAO,aAAa;oFAAC;;;;;;;;;;;;;kFAClE,6LAAC;wEAAI,WAAU;;4EAAiB;4EAAS,OAAO,YAAY;4EAAC;;;;;;;;;;;;;;;;;;sEAGjE,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EAAuB,KAAK,KAAK,CAAC;4EAAqB;;;;;;;kFACtE,6LAAC,uIAAA,CAAA,WAAQ;wEAAC,OAAO;wEAAqB,WAAU;;;;;;;;;;;;;;;;;sEAGpD,6LAAC,oIAAA,CAAA,YAAS;sEACP,eAAe,OAAO,YAAY,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;;;;;;sEAEhF,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW,OAAO,iBAAiB,IAAI;;;;;;;;;;;sEAExD,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAtCX,OAAO,SAAS;;;;;4CA4CnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpB;GAvTwB;;QAOK,kIAAA,CAAA,UAAO;;;KAPZ", "debugId": null}}]}