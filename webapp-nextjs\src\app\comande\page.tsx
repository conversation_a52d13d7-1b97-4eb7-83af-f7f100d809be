'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { comandeApi, responsabiliApi } from '@/lib/api'
import { Comanda, Responsabile } from '@/types'
import {
  ClipboardList,
  Plus,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Play,
  Pause,
  Square,
  Eye,
  Edit,
  Trash2,
  Loader2
} from 'lucide-react'

export default function ComandePage() {
  const [selectedTab, setSelectedTab] = useState('active')
  const [comande, setComande] = useState<Comanda[]>([])
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica comande e responsabili dal backend
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      const cantiereId = cantiere?.id_cantiere || user?.id_utente
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      const [comandeData, responsabiliData] = await Promise.all([
        comandeApi.getComande(cantiereId),
        responsabiliApi.getResponsabili(cantiereId)
      ])

      setComande(comandeData)
      setResponsabili(responsabiliData)
    } catch (error: any) {
      console.error('Errore caricamento dati:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (stato: string) => {
    switch (stato) {
      case 'completata':
        return <Badge className="bg-green-100 text-green-800">Completata</Badge>
      case 'in_corso':
        return <Badge className="bg-blue-100 text-blue-800">In Corso</Badge>
      case 'pianificata':
        return <Badge className="bg-yellow-100 text-yellow-800">Pianificata</Badge>
      case 'sospesa':
        return <Badge className="bg-red-100 text-red-800">Sospesa</Badge>
      default:
        return <Badge variant="secondary">{stato}</Badge>
    }
  }

  const getTipoBadge = (tipo: string) => {
    const colors = {
      'POSA': 'bg-blue-100 text-blue-800',
      'COLLEGAMENTO_PARTENZA': 'bg-green-100 text-green-800',
      'COLLEGAMENTO_ARRIVO': 'bg-purple-100 text-purple-800',
      'CERTIFICAZIONE': 'bg-orange-100 text-orange-800'
    }
    return <Badge className={colors[tipo as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>{tipo}</Badge>
  }

  const filteredComande = comande.filter(comanda => {
    switch (selectedTab) {
      case 'active':
        return comanda.stato === 'IN_CORSO' || comanda.stato === 'ASSEGNATA' || comanda.stato === 'CREATA'
      case 'completed':
        return comanda.stato === 'COMPLETATA'
      case 'all':
        return true
      default:
        return true
    }
  })

  const stats = {
    totali: comande.length,
    in_corso: comande.filter(c => c.stato === 'IN_CORSO').length,
    completate: comande.filter(c => c.stato === 'COMPLETATA').length,
    pianificate: comande.filter(c => c.stato === 'CREATA' || c.stato === 'ASSEGNATA').length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="container space-y-6">
        
        {/* Action button */}
        <div className="flex justify-end mb-6">
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Nuova Comanda
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Totali</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
                </div>
                <ClipboardList className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">In Corso</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.in_corso}</p>
                </div>
                <Play className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Completate</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completate}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Pianificate</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pianificate}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Comande List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Elenco Comande</CardTitle>
                  <div className="flex gap-2">
                    {[
                      { key: 'active', label: 'Attive' },
                      { key: 'completed', label: 'Completate' },
                      { key: 'all', label: 'Tutte' }
                    ].map((tab) => (
                      <Button
                        key={tab.key}
                        variant={selectedTab === tab.key ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedTab(tab.key)}
                      >
                        {tab.label}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Caricamento comande...
                    </div>
                  </div>
                ) : error ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      {error}
                    </div>
                  </div>
                ) : filteredComande.length === 0 ? (
                  <div className="text-center py-8 text-slate-500">
                    Nessuna comanda trovata
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredComande.map((comanda) => (
                      <Card key={comanda.codice_comanda} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div>
                              <h4 className="font-semibold text-slate-900">{comanda.codice_comanda}</h4>
                              <p className="text-sm text-slate-600">{comanda.descrizione || 'Nessuna descrizione'}</p>
                            </div>
                            <div className="flex gap-2">
                              {getTipoBadge(comanda.tipo_comanda)}
                              {getStatusBadge(comanda.stato)}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm">
                            <div>
                              <p className="text-slate-500">Responsabile</p>
                              <p className="font-medium">{comanda.responsabile || 'Non assegnato'}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Team</p>
                              <p className="font-medium">{comanda.numero_componenti_squadra || 0} persone</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Creazione</p>
                              <p className="font-medium">{new Date(comanda.data_creazione).toLocaleDateString('it-IT')}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Scadenza</p>
                              <p className="font-medium">
                                {comanda.data_scadenza ? new Date(comanda.data_scadenza).toLocaleDateString('it-IT') : 'Non definita'}
                              </p>
                            </div>
                          </div>

                          {comanda.data_completamento && (
                            <div className="mb-3 p-2 bg-green-50 rounded-lg">
                              <p className="text-sm text-green-700">
                                Completata il {new Date(comanda.data_completamento).toLocaleDateString('it-IT')}
                              </p>
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4 mr-1" />
                              Dettagli
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4 mr-1" />
                              Modifica
                            </Button>
                            {comanda.stato === 'IN_CORSO' && (
                              <Button variant="ghost" size="sm">
                                <Pause className="h-4 w-4 mr-1" />
                                Sospendi
                              </Button>
                            )}
                            {(comanda.stato === 'CREATA' || comanda.stato === 'ASSEGNATA') && (
                              <Button variant="ghost" size="sm">
                                <Play className="h-4 w-4 mr-1" />
                                Avvia
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Responsabili Sidebar */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Responsabili
                </CardTitle>
                <CardDescription>Gestione responsabili di cantiere</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Caricamento...
                  </div>
                ) : (
                  <>
                    <div className="space-y-4">
                      {responsabili.map((responsabile) => {
                        const comandeAttive = comande.filter(c =>
                          c.responsabile === responsabile.nome_responsabile &&
                          (c.stato === 'IN_CORSO' || c.stato === 'ASSEGNATA')
                        ).length

                        return (
                          <div key={responsabile.id_responsabile} className="p-3 bg-slate-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-slate-900">{responsabile.nome_responsabile}</h4>
                              <Badge variant={comandeAttive > 0 ? 'default' : 'secondary'}>
                                {comandeAttive} attive
                              </Badge>
                            </div>
                            <div className="text-sm text-slate-600 space-y-1">
                              {responsabile.numero_telefono && <p>{responsabile.numero_telefono}</p>}
                              {responsabile.mail && <p>{responsabile.mail}</p>}
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    <Button variant="outline" size="sm" className="w-full mt-4">
                      <Plus className="h-4 w-4 mr-2" />
                      Nuovo Responsabile
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

      </div>
    </div>
  )
}
